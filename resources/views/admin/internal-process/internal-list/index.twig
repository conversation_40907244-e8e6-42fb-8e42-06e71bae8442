{% extends 'components/admin_sidebar.twig' %}

{% block styles %}
<link rel="stylesheet" href="{{ asset('css/internal/internal.css') }}">
{% endblock %}


{% block page_content %}

<section class="internal-list-section">
    <div class="d-flex flex-column p-4">
        <h4 class="custom-title mb-1">Dahili <PERSON>esi</h4>
        <div class="d-flex align-items-center route-links">
            <a>Dahili <PERSON>lemler<PERSON></a>
            <span class="mx-2">></span>
            <a href="/admin/dahili-listesi">Dahili <PERSON>esi</a>
        </div>
    </div>
    <hr class="custom-hr mt-0">
    <div class="p-4">
        <div class="white-container p-0">
            <div class="d-flex align-items-center justify-content-between mt-4 px-4 internal-header">
                <div class="d-flex flex-column">
                    <h4 class="table-title"><PERSON><PERSON><PERSON></h4>
                    <small class="me-2 sm-text d-none mobile-visible">Dahili Limitiniz: 5</small>
                </div>
                <div class="d-flex align-items-center">
                    <small class="me-2 sm-text d-none d-md-inline">Dahili Limitiniz: 5</small>
                    <!-- Desktop Buttons -->
                    <div class="action-buttons d-flex">
                        <button class="edit-btn">
                            <span class="iconify" data-icon="hugeicons:edit-02" data-inline="false"></span>
                            <span class="text">Düzenle</span>
                        </button>
                        <button class="add-new-btn mx-2">
                            <span class="iconify" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                            <span class="text">Ekle</span>
                        </button>
                        <button class="delete-btn">
                            <span class="iconify" data-icon="hugeicons:delete-03" data-inline="false"></span>
                            <span class="text">Sil</span>
                        </button>
                    </div>
                    <!-- Mobile Menu -->
                    <div class="responsive-menu" style="display: none;">
                        <button class="menu-toggle">
                            <span class="iconify" data-icon="iconamoon:menu-kebab-vertical-fill" data-inline="false"></span>
                        </button>
                        <div class="dropdown-menu">
                            <div class="dropdown-menu-item edit-menu-item" id="mobileEditBtn">
                                <span class="iconify edit-btn" data-icon="hugeicons:edit-02" data-inline="false"></span>
                                <span class="text">Düzenle</span>
                            </div>
                            <div class="dropdown-menu-item add-menu-item">
                                <span class="iconify add-new-btn" data-icon="hugeicons:plus-sign" data-inline="false"></span>
                                <span class="text">Ekle</span>
                            </div>
                            <div class="dropdown-menu-item delete-menu-item">
                                <span class="iconify delete-btn" data-icon="hugeicons:delete-03" data-inline="false"></span>
                                <span class="text">Sil</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <hr class="custom-hr">
            <div class="d-flex align-items-center px-4 internal-choose">
                <!-- Desktop filters -->
                <div class="d-none d-md-flex align-items-center w-100">
                    <input type="text" class="form-control me-2 w-auto" style="min-width: 200px;" id="internalNo" name="internalNo" placeholder="Dahili No ile ara...">
                    <select class="form-select w-auto" style="min-width: 200px;" id="chooseGroup" name="chooseGroup">
                        <option disabled selected>Grup Seç</option>
                        <option>Arama</option>
                    </select>
                </div>
                
                <!-- Mobile filter button -->
                <div class="d-md-none d-flex justify-content-between w-100">
                    <button class="blue-btn flex-grow-1 me-2" id="filterButton">Filtrele</button>
                    <button class="filter-icon-btn" id="filterIconButton">
                        <span class="iconify" data-icon="oui:filter" data-inline="false"></span>
                    </button>
                </div>
            </div>
            <div class="custom-data-table-wrapper">
                {% set headers = [
                  { type: 'checkbox', name: 'checkAll', class: 'no-sort' },
                  'Dahili No',
                  'Dahili Kullanıcı Adı',
                  'Parola',
                  'Agent Parolası',
                  'Domain Bilgisi',
                  'Dahili Açıklaması',
                  'E-Posta',
                  'Görünen Numara',
                  'Grup Adı',
                  'Durum'
                ] %}

                {% set rows = [] %}
                {% for dial in dial_numbers %}
                    {% set row = {
                      'checkbox': { type: 'checkbox', value: dial.id },
                      'internalNo': dial.dial_code,
                      'internalUsername': dial.user.name,
                      'internalPassword': dial.password,
                      'agentPassword': dial.agentPassword,
                      'domainInfo': dial.domain_info,
                      'internalDescription': dial.internal_description,
                      'email': dial.email,
                      'displayNumber': dial.display_number,
                      'groupName': dial.group_name,
                      'status': dial.status
                    } %}
                    {% set rows = rows|merge([row]) %}
                {% endfor %}

                {% include 'components/table.twig' with {
                  id: 'internalTable',
                  headers: headers,
                  rows: rows
                } %}
            </div>
        </div>
    </div>
</section>


<div class="modal fade" id="addModal" tabindex="-1" aria-labelledby="addModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <form style="display: contents;">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addModalLabel">Dahili Ekle</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Tabs -->
                    <ul class="nav nav-tabs" id="editTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab" aria-controls="general" aria-selected="true">Genel Ayarlar</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="permissions-tab" data-bs-toggle="tab" data-bs-target="#permissions" type="button" role="tab" aria-controls="permissions" aria-selected="false">Yetkilendirmeler</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="status-tab" data-bs-toggle="tab" data-bs-target="#status-content" type="button" role="tab" aria-controls="status-content" aria-selected="false">Durum Yönlendirmeleri</button>
                        </li>
                    </ul>
                    
                    <!-- Tab Content -->
                    <div class="tab-content" id="editTabsContent">
                        <!-- Genel Ayarlar Tab -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel" aria-labelledby="general-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="internalGroup" class="form-label">Dahili Grubu</label>
                                    <select class="form-select" id="internalGroup" name="internalGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Yönetim</option>
                                        <option>İK</option>
                                        <option>Teknik</option>
                                        <option>Müşteri Hizmetleri</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="internalNumber" class="form-label">Dahili No</label>
                                    <input type="text" class="form-control" id="internalNumber" name="internalNumber">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="internalUsername" class="form-label">Dahili Kullanıcı Adı</label>
                                    <input type="text" class="form-control" id="internalUsername" name="internalUsername">
                                </div>
                                <div class="col-md-6">
                                    <label for="email" class="form-label">E-Posta</label>
                                    <input type="email" class="form-control" id="email" name="email">
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="internalPassword" class="form-label">Dahili Parolası</label>
                                    <div class="d-flex align-items-center">
                                        <input type="text" class="form-control" id="internalPassword" name="internalPassword">
                                        <button class="generate-password-btn" type="button">Parola Üret</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-12">
                                    <label for="agentPassword" class="form-label">Agent Parolası</label>
                                    <div class="d-flex align-items-center">
                                        <input type="text" class="form-control" id="agentPassword" name="agentPassword">
                                        <button class="generate-password-btn" type="button">Parola Üret</button>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">Durum</label>
                                    <select class="form-select" id="status" name="status">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="active">Aktif</option>
                                        <option value="waiting">Bekliyor</option>
                                        <option value="deactive">Deaktif</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="codec" class="form-label">Kodek</label>
                                    <select class="form-select" id="codec" name="codec">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>G.711</option>
                                        <option>G.722</option>
                                        <option>G.729</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="displaySearch" class="form-label">Görünen Arama</label>
                                    <select class="form-select" id="displaySearch" name="displaySearch">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Seçenek 1</option>
                                        <option>Seçenek 2</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="searchPrefixGroup" class="form-label">Arama Prefix Grubu</label>
                                    <select class="form-select" id="searchPrefixGroup" name="searchPrefixGroup">
                                        <option selected disabled>Seçiniz...</option>
                                        <option>Grup 1</option>
                                        <option>Grup 2</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Yetkilendirmeler Tab -->
                        <div class="tab-pane fade" id="permissions" role="tabpanel" aria-labelledby="permissions-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="channelLimit" class="form-label">Kanal Limiti</label>
                                    <select class="form-select" id="channelLimit" name="channelLimit">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="1">1</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                        <option value="unlimited">Sınırsız</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="timeoutDuration" class="form-label">Timeout Çalma Süresi (Sn)</label>
                                    <input type="number" class="form-control" id="timeoutDuration" name="timeoutDuration" min="0" value="30">
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-12">
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="internalCallAllowed">
                                        <label class="custom-check me-2" for="internalCallAllowed">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="internalCallAllowed">
                                            Dahili Arama Yapabilir
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="externalCallAllowed">
                                        <label class="custom-check me-2" for="externalCallAllowed">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="externalCallAllowed">
                                            Dışarı Arama Yapabilir
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="receiveExternalCallAllowed">
                                        <label class="custom-check me-2" for="receiveExternalCallAllowed">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="receiveExternalCallAllowed">
                                            Dışarıdan Arama Alabilir
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="incomingCallRecording">
                                        <label class="custom-check me-2" for="incomingCallRecording">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="incomingCallRecording">
                                            Gelen Çağrılarda Ses Kaydı
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="dialableFromIVR">
                                        <label class="custom-check me-2" for="dialableFromIVR">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="dialableFromIVR">
                                            IVR'dan Tuşlanabilir
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="callWaitingDisabled">
                                        <label class="custom-check me-2" for="callWaitingDisabled">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="callWaitingDisabled">
                                            Arama Bekletme Kapalı
                                        </label>
                                    </div>
                                    
                                    <div class="mb-2 custom-checkbox d-flex align-items-center">
                                        <input type="checkbox" id="showName">
                                        <label class="custom-check me-2" for="showName">
                                            <span class="iconify" data-icon="bi:check" data-inline="false"></span>
                                        </label>
                                        <label class="form-check-label" for="showName">
                                            İsim Göster <small class="text-muted">(Görünen numara olarak dahili açıklamasındaki ismi gösterir.)</small>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Durum Yönlendirmeleri Tab -->
                        <div class="tab-pane fade" id="status-content" role="tabpanel" aria-labelledby="status-tab">
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="doNotDisturb" class="form-label mb-0">Rahatsız Etmeyin</label>
                                        <span class="iconify ms-1" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <select class="form-select" id="doNotDisturb" name="doNotDisturb">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="active">Aktif</option>
                                        <option value="deactive">Deaktif</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center mb-2">
                                        <label for="workSchedule" class="form-label mb-0">Mesai Şablonu</label>
                                        <span class="iconify ms-2" data-icon="hugeicons:help-circle" data-inline="false" style="font-size: 20px; color: #4B67C2;"></span>
                                    </div>
                                    <input type="text" class="form-control" id="workSchedule" name="workSchedule">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="allCalls" class="form-label mb-2">Tüm Çağrıları</label>
                                    <select class="form-select" id="allCalls" name="allCalls">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="reject">Reddet</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="noAnswer" class="form-label mb-2">Cevap Yoksa</label>
                                    <input type="text" class="form-control" id="noAnswer" name="noAnswer">
                                </div>
                            </div>
                            
                            <div class="row mb-2">
                                <div class="col-md-6">
                                    <label for="ifBusy" class="form-label mb-2">Meşgulse</label>
                                    <select class="form-select" id="ifBusy" name="ifBusy">
                                        <option selected disabled>Seçiniz...</option>
                                        <option value="forward">Yönlendir</option>
                                        <option value="voicemail">Sesli Mesaj</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="ifClosed" class="form-label mb-2">Kapalıysa</label>
                                    <input type="text" class="form-control" id="ifClosed" name="ifClosed">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="white-btn" style="border: 1px solid #4B67C2; color: #4B67C2;" data-bs-dismiss="modal">Vazgeç</button>
                    <button type="button" class="blue-btn">Kaydet</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Filter Modal for Mobile -->
<div class="modal fade" id="filterModal" tabindex="-1" aria-labelledby="filterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header px-4">
                <h5 class="modal-title" id="filterModalLabel">Filtreler</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body px-4">
                <div class="mb-2">
                    <label for="mobileInternalNo" class="form-label">Dahili No ile ara</label>
                    <input type="text" class="form-control" id="mobileInternalNo" name="mobileInternalNo" placeholder="Dahili No ile ara...">
                </div>
                <div class="mb-2">
                    <label for="mobileChooseInternal" class="form-label">Grup Seç</label>
                    <select class="form-select" id="mobileChooseInternal" name="mobileChooseInternal">
                        <option disabled selected>Grup Seç</option>
                        <option>Arama</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer px-4">
                <button type="button" class="blue-btn w-100" id="applyFilters">Kaydet</button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        var table = $('#internalTable').DataTable({
            searching: false,
            pageLength: 10,
            language: {
                lengthMenu: '_MENU_',
                info: '_START_ ile _END_ arası gösteriliyor, toplam _TOTAL_ kayıt.'
            },
            columnDefs: [
                { orderable: false, targets: 0 } // İlk sütunda sıralama kapalı
            ],
            drawCallback: function(settings) {
                document.querySelectorAll('.dt-paging-button.current').forEach(function(btn) {
                    btn.style.color = '#FFFFFF';
                    btn.querySelectorAll('*').forEach(function(child) {
                        child.style.color = '#FFFFFF';
                    });
                });
            }
        });
        // dt-length'i dt-paging'in soluna taşı
        var length = $(table.table().container()).find('.dt-length');
        var paging = $(table.table().container()).find('.dt-paging');
        paging.before(length);

        // Edit ve delete butonlarını başlangıçta disabled yap
        window.editBtn = document.querySelector('.edit-btn');
        window.deleteBtn = document.querySelector('.delete-btn');
        
        window.editBtn.disabled = true;
        window.deleteBtn.disabled = true;
        
        window.editBtn.classList.add('disabled-btn');
        window.deleteBtn.classList.add('disabled-btn');
        
        // İlk sayfa yüklendiğinde buton durumlarını kontrol et
        updateButtonStates();
        
        // Mobil menü öğelerinin durumunu güncelle
        if (typeof window.updateMobileMenuItems === 'function') {
            window.updateMobileMenuItems();
        }
        
        // Seçim durumunu kontrol et ve butonları güncelle
        function updateButtonStates() {
            var checkedBoxes = document.querySelectorAll('#internalTable tbody input[type="checkbox"]:checked');
            
            // Edit butonu sadece bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length === 1) {
                window.editBtn.disabled = false;
                window.editBtn.classList.remove('disabled-btn');
            } else {
                window.editBtn.disabled = true;
                window.editBtn.classList.add('disabled-btn');
            }
            
            // Delete butonu en az bir öğe seçiliyse aktif olacak
            if (checkedBoxes.length > 0) {
                window.deleteBtn.disabled = false;
                window.deleteBtn.classList.remove('disabled-btn');
            } else {
                window.deleteBtn.disabled = true;
                window.deleteBtn.classList.add('disabled-btn');
            }
        }
        
        // Tümünü seç/deselect
        $('#checkAll').on('change', function() {
            var checked = this.checked;
            $('#internalTable tbody input[type="checkbox"]').prop('checked', checked);
            updateButtonStates();
        });
        
        // Herhangi bir checkbox değiştiğinde
        $(document).on('change', '#internalTable tbody input[type="checkbox"]', function() {
            updateButtonStates();
            
            // Eğer tüm checkboxlar seçili değilse, checkAll'ı da unchecked yap
            if (!this.checked) {
                $('#checkAll').prop('checked', false);
            } else {
                // Eğer tüm checkboxlar seçili ise, checkAll'ı da checked yap
                var allChecked = $('#internalTable tbody input[type="checkbox"]').length === 
                                 $('#internalTable tbody input[type="checkbox"]:checked').length;
                $('#checkAll').prop('checked', allChecked);
            }
        });
        
        // Edit butonuna tıklandığında
        window.editBtn.addEventListener('click', function() {
            if (!this.disabled) {
                // Modal'ı aç
                var editModal = new bootstrap.Modal(document.getElementById('addModal'));
                // Başlığı değiştir
                document.getElementById('addModalLabel').textContent = 'Dahili Düzenle';
                editModal.show();
            }
        });
        
        // Add new button modal functionality
        document.querySelector('.add-new-btn').addEventListener('click', function() {
            // Modalı açmadan önce başlığı Dahili Ekle olarak ayarla
            document.getElementById('addModalLabel').textContent = 'Dahili Ekle';
            
            // Form alanlarını temizle
            const formInputs = document.querySelectorAll('#addModal input, #addModal select');
            formInputs.forEach(input => {
                if (input.type === 'checkbox') {
                    input.checked = false;
                } else if (input.tagName === 'SELECT') {
                    input.selectedIndex = 0;
                } else {
                    input.value = '';
                }
            });
            
            var addModal = new bootstrap.Modal(document.getElementById('addModal'));
            addModal.show();
        });
        
        // Delete butonuna tıklandığında
        window.deleteBtn.addEventListener('click', function() {
            if (!this.disabled) {
                var checkedBoxes = document.querySelectorAll('#internalTable tbody input[type="checkbox"]:checked');
                if (checkedBoxes.length > 0) {
                    // Global silme modalını göster
                    const deleteModal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));
                    deleteModal.show();
                    
                    // Not: Silme işlemi sonradan eklenecek
                }
            }
        });
                
        // Buton durumları değiştiğinde mobil menüyü de güncelle
        const originalUpdateButtonStates = updateButtonStates;
        updateButtonStates = function() {
            originalUpdateButtonStates();
            if (typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        };
        
        // Pencere boyutu değiştiğinde responsive menu durumunu güncelle
        window.addEventListener('resize', function() {
            if (window.innerWidth <= 768 && typeof window.updateMobileMenuItems === 'function') {
                window.updateMobileMenuItems();
            }
        });
    });
</script>


<script>
document.addEventListener('DOMContentLoaded', function() {
    var select = document.getElementById('chooseInternal');
    var mobileSelect = document.getElementById('mobileChooseInternal');
    
    function updateSelectColor() {
        if(select.value === '' || select.value === undefined || select.value === null || select.selectedIndex === 0) {
            select.style.color = '#94A3B8';
        } else {
            select.style.color = '#111827';
        }
    }
    updateSelectColor();
    select.addEventListener('change', updateSelectColor);
    
    // Mobile select color update
    function updateMobileSelectColor() {
        if(mobileSelect.value === '' || mobileSelect.value === undefined || mobileSelect.value === null || mobileSelect.selectedIndex === 0) {
            mobileSelect.style.color = '#94A3B8';
        } else {
            mobileSelect.style.color = '#111827';
        }
    }
    updateMobileSelectColor();
    mobileSelect.addEventListener('change', updateMobileSelectColor);
    
    // Filter modal functionality
    const filterButton = document.getElementById('filterButton');
    const filterIconButton = document.getElementById('filterIconButton');
    const filterModal = new bootstrap.Modal(document.getElementById('filterModal'));
    const applyFiltersButton = document.getElementById('applyFilters');
    const mobileInternalNo = document.getElementById('mobileInternalNo');
    const desktopInternalNo = document.getElementById('internalNo');
    
    // Sync filter values between desktop and mobile
    desktopInternalNo.addEventListener('input', function() {
        mobileInternalNo.value = this.value;
    });
    
    select.addEventListener('change', function() {
        mobileSelect.value = this.value;
        updateMobileSelectColor();
    });
    
    // Only icon button shows filter modal
    if (filterIconButton) {
        filterIconButton.addEventListener('click', function() {
            filterModal.show();
        });
    }
});
</script>

{% endblock %}